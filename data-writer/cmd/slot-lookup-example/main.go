package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
)

func main() {
	// Initialize database connection
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	repo := db.Get()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Example 1: Look up a single slot timestamp
	exampleSlot := uint64(345600000)
	
	fmt.Printf("Looking up timestamp for slot %d...\n", exampleSlot)
	
	// Using GetSlotTimestamp (returns error if not found)
	timestamp, err := repo.GetSlotTimestamp(ctx, exampleSlot)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Slot %d has timestamp: %d (Unix seconds)\n", exampleSlot, timestamp)
		fmt.Printf("Human readable time: %s\n", time.Unix(timestamp, 0).UTC().Format(time.RFC3339))
	}

	// Using GetSlotTimestampOrNil (returns nil if not found, no error)
	timestampPtr, err := repo.GetSlotTimestampOrNil(ctx, exampleSlot)
	if err != nil {
		fmt.Printf("Database error: %v\n", err)
	} else if timestampPtr == nil {
		fmt.Printf("Slot %d not found in database\n", exampleSlot)
	} else {
		fmt.Printf("Slot %d found with timestamp: %d\n", exampleSlot, *timestampPtr)
	}

	// Example 2: Insert some sample data
	fmt.Println("\nInserting sample slot-timestamp mappings...")
	
	sampleData := map[uint64]int64{
		345600000: 1640995200, // 2022-01-01 00:00:00 UTC
		345600001: 1640995260, // 2022-01-01 00:01:00 UTC  
		345600002: 1640995320, // 2022-01-01 00:02:00 UTC
	}

	// Insert using batch operation
	if err := repo.BatchInsertSlotTimestamps(ctx, sampleData); err != nil {
		log.Printf("Failed to insert sample data: %v", err)
	} else {
		fmt.Println("Sample data inserted successfully")
	}

	// Example 3: Look up the inserted data
	fmt.Println("\nLooking up inserted data...")
	for slot := range sampleData {
		timestamp, err := repo.GetSlotTimestamp(ctx, slot)
		if err != nil {
			fmt.Printf("Failed to get timestamp for slot %d: %v\n", slot, err)
		} else {
			humanTime := time.Unix(timestamp, 0).UTC().Format("2006-01-02 15:04:05 UTC")
			fmt.Printf("Slot %d -> Timestamp %d (%s)\n", slot, timestamp, humanTime)
		}
	}

	// Example 4: Try to look up a non-existent slot
	fmt.Println("\nTrying to look up non-existent slot...")
	nonExistentSlot := uint64(999999999)
	
	// This will return an error
	_, err = repo.GetSlotTimestamp(ctx, nonExistentSlot)
	if err != nil {
		fmt.Printf("Expected error for non-existent slot: %v\n", err)
	}

	// This will return nil without error
	timestampPtr, err = repo.GetSlotTimestampOrNil(ctx, nonExistentSlot)
	if err != nil {
		fmt.Printf("Unexpected error: %v\n", err)
	} else if timestampPtr == nil {
		fmt.Printf("Slot %d not found (returned nil as expected)\n", nonExistentSlot)
	}

	fmt.Println("\nExample completed successfully!")
}
