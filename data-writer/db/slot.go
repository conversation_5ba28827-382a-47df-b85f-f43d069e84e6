package db

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

// GetSlotTimestamp retrieves the timestamp for a given slot from the slot_times table.
// Returns the timestamp in Unix seconds, or an error if the slot is not found or if there's a database error.
func (r *Repo) GetSlotTimestamp(ctx context.Context, slot uint64) (int64, error) {
	query := `SELECT timestamp FROM slot_times WHERE slot = $1`

	var timestamp int64
	err := r.pool.QueryRow(ctx, query, slot).Scan(&timestamp)
	if err != nil {
		if err == pgx.ErrNoRows {
			return 0, fmt.Errorf("slot %d not found in slot_times table", slot)
		}
		return 0, fmt.Errorf("failed to query slot timestamp for slot %d: %v", slot, err)
	}

	return timestamp, nil
}

// GetSlotTimestampOrNil retrieves the timestamp for a given slot from the slot_times table.
// Returns the timestamp in Unix seconds, or nil if the slot is not found.
// Returns an error only for database connection issues.
func (r *Repo) GetSlotTimestampOrNil(ctx context.Context, slot uint64) (*int64, error) {
	query := `SELECT timestamp FROM slot_times WHERE slot = $1`

	var timestamp int64
	err := r.pool.QueryRow(ctx, query, slot).Scan(&timestamp)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil // Slot not found, return nil without error
		}
		return nil, fmt.Errorf("failed to query slot timestamp for slot %d: %v", slot, err)
	}

	return &timestamp, nil
}

// InsertSlotTimestamp inserts a new slot-timestamp mapping into the slot_times table.
// Uses ON CONFLICT DO NOTHING to handle duplicate inserts gracefully.
func (r *Repo) InsertSlotTimestamp(ctx context.Context, slot uint64, timestamp int64) error {
	query := `INSERT INTO slot_times (slot, timestamp) VALUES ($1, $2) ON CONFLICT (slot) DO NOTHING`

	_, err := r.pool.Exec(ctx, query, slot, timestamp)
	if err != nil {
		return fmt.Errorf("failed to insert slot timestamp for slot %d: %v", slot, err)
	}

	return nil
}

// BatchInsertSlotTimestamps inserts multiple slot-timestamp mappings in a single transaction.
// Uses the same temp table pattern as other batch operations in the codebase.
func (r *Repo) BatchInsertSlotTimestamps(ctx context.Context, slotTimestamps map[uint64]int64) error {
	if len(slotTimestamps) == 0 {
		return nil // Nothing to insert
	}

	return r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
		// Create temporary table
		if _, err := tx.Exec(ctx, "CREATE TEMPORARY TABLE temp_slot_times (LIKE slot_times) ON COMMIT DROP"); err != nil {
			return fmt.Errorf("failed to create temporary table: %v", err)
		}

		// Prepare data for batch insert
		rows := make([][]interface{}, 0, len(slotTimestamps))
		for slot, timestamp := range slotTimestamps {
			rows = append(rows, []interface{}{slot, timestamp})
		}

		// Copy data to temporary table
		_, err := tx.CopyFrom(ctx,
			pgx.Identifier{"temp_slot_times"},
			[]string{"slot", "timestamp"},
			pgx.CopyFromRows(rows))
		if err != nil {
			return fmt.Errorf("failed to copy to temporary table: %v", err)
		}

		// Insert from temporary table with conflict handling
		if _, err := tx.Exec(ctx, `
			INSERT INTO slot_times
			SELECT * FROM temp_slot_times
			ON CONFLICT (slot) DO NOTHING`); err != nil {
			return fmt.Errorf("failed to insert from temporary table: %v", err)
		}

		return nil
	})
}
